using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ECommerce.Data.Data;
using ECommerce.Data.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace ECommerce.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CartController : ControllerBase
    {
        // Veri Tabanı İşlemleri (Veritabanıdan gelen veriler)
        private readonly ECommerceDbContext _context;
        public CartController(ECommerceDbContext context)
        {
            _context = context;
        }

        // Tüm Kartları Listeleme Metodu
        [HttpGet]
        public async Task<ActionResult<List<Cart>>> GetAllCart()
        {
            var cart = await _context.Carts.ToListAsync();
            return Ok(cart);
        }
        
    }
}