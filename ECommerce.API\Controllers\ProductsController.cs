using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ECommerce.Data.Models;
using Microsoft.AspNetCore.Mvc;
using ECommerce.Data.Data;
using Microsoft.EntityFrameworkCore;
using ECommerce.API.Models;

namespace ECommerce.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProductsController : ControllerBase
    {
        // Veri Tabanı İşlemleri (Veritabanıdan gelen veriler) 
        private readonly ECommerceDbContext _context;

        public ProductsController(ECommerceDbContext context)
        {
            _context = context;
        }

        // Tüm Ürünleri Getirme Methodu
        [HttpGet]
        public async Task<ActionResult<List<Product>>> GetAllProducts()
        {
            var products = await _context.Products.ToListAsync();
            return Ok(products);
        }

        [HttpPost]
        public async Task<ActionResult<Product>> Create([FromForm] ProductCreateRequest request)
        {
            try
            {
                // Model validation
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // CategoryId'nin geçerli olup olmadığını kontrol et
                var categoryExists = await _context.Categories.AnyAsync(c => c.Id == request.CategoryId);
                if (!categoryExists)
                {
                    return BadRequest($"CategoryId {request.CategoryId} bulunamadı.");
                }

                string? imageUrl = null;

                // Dosya yükleme işlemi
                if (request.ImageFile != null && request.ImageFile.Length > 0)
                {
                    // Dosya uzantısını kontrol et
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                    var fileExtension = Path.GetExtension(request.ImageFile.FileName).ToLowerInvariant();

                    if (!allowedExtensions.Any(ext => ext == fileExtension))
                    {
                        return BadRequest("Sadece resim dosyaları yüklenebilir (.jpg, .jpeg, .png, .gif, .webp)");
                    }

                    // Dosya boyutunu kontrol et (5MB limit)
                    if (request.ImageFile.Length > 5 * 1024 * 1024)
                    {
                        return BadRequest("Dosya boyutu 5MB'dan büyük olamaz.");
                    }

                    // Uploads klasörünü oluştur
                    var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "products");
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    // Benzersiz dosya adı oluştur
                    var fileName = Guid.NewGuid().ToString() + fileExtension;
                    var filePath = Path.Combine(uploadsFolder, fileName);

                    // Dosyayı kaydet
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await request.ImageFile.CopyToAsync(stream);
                    }

                    imageUrl = $"/uploads/products/{fileName}";
                }

                // Product nesnesini oluştur
                var product = new Product
                {
                    Name = request.Name,
                    Description = request.Description,
                    Price = request.Price,
                    Stock = request.Stock,
                    ImageUrl = imageUrl,
                    CategoryId = request.CategoryId,
                    IsActive = request.IsActive,
                    CreatedDate = DateTime.Now
                };

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetById), new { id = product.Id }, product);
            }
            catch (Exception ex)
            {
                return BadRequest($"Ürün ekleme hatası: {ex.Message}");
            }
        }

        // GET by ID (POST'ta dönüş için)
        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetById(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null) return NotFound();
            return Ok(product);
        }

        // PUT: api/products/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, Product product)
        {
            if (id != product.Id) return BadRequest();

            _context.Entry(product).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await _context.Products.AnyAsync(p => p.Id == id))
                    return NotFound();

                throw;
            }
        }

        // DELETE: api/products/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null) return NotFound();

            _context.Products.Remove(product);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        

    }
}