using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ECommerce.Data.Models;
using Microsoft.AspNetCore.Mvc;
using ECommerce.Data.Data;
using Microsoft.EntityFrameworkCore;

namespace ECommerce.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProductsController : ControllerBase
    {
        // Veri Tabanı İşlemleri (Veritabanıdan gelen veriler) 
        private readonly ECommerceDbContext _context;

        public ProductsController(ECommerceDbContext context)
        {
            _context = context;
        }

        // Tüm Ürünleri Getirme Methodu
        [HttpGet]
        public async Task<ActionResult<List<Product>>> GetAllProducts()
        {
            var products = await _context.Products.ToListAsync();
            return Ok(products);
        }

        [HttpPost]
        public async Task<ActionResult<Product>> Create(Product product)
        {
            try
            {
                // Navigation property'leri null yap (sadece ID'ler kullanılacak)
                product.Category = null;
                product.OrderItems = null;
                product.CartItems = null;

                // Model validation (navigation property'ler hariç)
                ModelState.Remove("Category");
                ModelState.Remove("OrderItems");
                ModelState.Remove("CartItems");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // CategoryId'nin geçerli olup olmadığını kontrol et
                var categoryExists = await _context.Categories.AnyAsync(c => c.Id == product.CategoryId);
                if (!categoryExists)
                {
                    return BadRequest($"CategoryId {product.CategoryId} bulunamadı.");
                }

                // CreatedDate'i set et (eğer set edilmemişse)
                if (product.CreatedDate == default(DateTime))
                {
                    product.CreatedDate = DateTime.Now;
                }

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetById), new { id = product.Id }, product);
            }
            catch (Exception ex)
            {
                return BadRequest($"Ürün ekleme hatası: {ex.Message}");
            }
        }

        // GET by ID (POST'ta dönüş için)
        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetById(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null) return NotFound();
            return Ok(product);
        }

        // PUT: api/products/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, Product product)
        {
            if (id != product.Id) return BadRequest();

            _context.Entry(product).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await _context.Products.AnyAsync(p => p.Id == id))
                    return NotFound();

                throw;
            }
        }

        // DELETE: api/products/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null) return NotFound();

            _context.Products.Remove(product);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        

    }
}