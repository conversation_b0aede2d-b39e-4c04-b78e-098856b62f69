using System.ComponentModel.DataAnnotations;

namespace ECommerce.API.Models
{
    public class ProductCreateRequest
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; }
        
        [MaxLength(1000)]
        public string? Description { get; set; }
        
        [Required]
        public decimal Price { get; set; }
        
        [Required]
        public int Stock { get; set; }
        
        [Required]
        public int CategoryId { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public IFormFile? ImageFile { get; set; }
    }
}
