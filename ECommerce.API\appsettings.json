{"ConnectionStrings": {"DefaultConnection": "Server=CELAL_KHALILOV\\SQLEXPRESS;Database=ECommerceDB;TrustServerCertificate=true;User Id=sa;Password=*;"}, "JwtSettings": {"Key": "this-is-a-very-long-secret-key-for-jwt-token-generation-minimum-256-bits", "Issuer": "ECommerceAPI", "Audience": "ECommerceWeb", "ExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}