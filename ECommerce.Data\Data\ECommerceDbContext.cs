using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ECommerce.Data.Models;
using Microsoft.EntityFrameworkCore;
using BCrypt.Net;

namespace ECommerce.Data.Data
{
    public class ECommerceDbContext : DbContext
    {
        public ECommerceDbContext(DbContextOptions<ECommerceDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Cart> Carts { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User Configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Email).IsRequired();
            });

            // Category Configuration
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
            });

            // Product Configuration
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasOne(p => p.Category)
                    .WithMany(c => c.Products)
                    .HasForeignKey(p => p.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Cart Configuration
            modelBuilder.Entity<Cart>(entity =>
            {
                entity.HasOne(c => c.User)
                    .WithOne(u => u.Cart)
                    .HasForeignKey<Cart>(c => c.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // CartItem Configuration
            modelBuilder.Entity<CartItem>(entity =>
            {
                entity.HasOne(ci => ci.Cart)
                    .WithMany(c => c.CartItems)
                    .HasForeignKey(ci => ci.CartId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ci => ci.Product)
                    .WithMany(p => p.CartItems)
                    .HasForeignKey(ci => ci.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Order Configuration
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasOne(o => o.User)
                    .WithMany(u => u.Orders)
                    .HasForeignKey(o => o.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // OrderItem Configuration
            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasOne(oi => oi.Order)
                    .WithMany(o => o.OrderItems)
                    .HasForeignKey(oi => oi.OrderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(oi => oi.Product)
                    .WithMany(p => p.OrderItems)
                    .HasForeignKey(oi => oi.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed Data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "Elektronik", Description = "Elektronik ürünler" },
                new Category { Id = 2, Name = "Giyim", Description = "Giyim ürünleri" },
                new Category { Id = 3, Name = "Kitap", Description = "Kitaplar" },
                new Category { Id = 4, Name = "Ev & Yaşam", Description = "Ev ve yaşam ürünleri" }
            );

            // Products
            modelBuilder.Entity<Product>().HasData(
                new Product { Id = 1, Name = "iPhone 15", Description = "Son model iPhone", Price = 45000, Stock = 50, CategoryId = 1, ImageUrl = "/images/iphone15.jpg", CreatedDate = new DateTime(2024, 1, 1) },
                new Product { Id = 2, Name = "Samsung Galaxy S24", Description = "Samsung telefon", Price = 35000, Stock = 30, CategoryId = 1, ImageUrl = "/images/galaxy-s24.jpg", CreatedDate = new DateTime(2024, 1, 1) },
                new Product { Id = 3, Name = "Polo T-Shirt", Description = "Erkek polo t-shirt", Price = 299, Stock = 100, CategoryId = 2, ImageUrl = "/images/polo-tshirt.jpg", CreatedDate = new DateTime(2024, 1, 1) },
                new Product { Id = 4, Name = "Jean Pantolon", Description = "Kadın jean pantolon", Price = 599, Stock = 75, CategoryId = 2, ImageUrl = "/images/jean-pantolon.jpg", CreatedDate = new DateTime(2024, 1, 1) },
                new Product { Id = 5, Name = "C# Programlama", Description = "C# öğrenme kitabı", Price = 89, Stock = 200, CategoryId = 3, ImageUrl = "/images/csharp-book.jpg", CreatedDate = new DateTime(2024, 1, 1) }
            );

            // Admin User
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    Password = "$2a$11$K8F9z4QoqzWUUKPPqjRzGOaOKEQEd7qUzUKhXGvWRkNgHJhqrMquu", // 123456
                    Role = "Admin",
                    CreatedDate = new DateTime(2024, 1, 1)
                }
            );
        }
    }
}