using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECommerce.Data.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace ECommerce.Web.Controllers
{
    public class AdminController : Controller
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<HomeController> _logger;

        public AdminController(ILogger<HomeController> logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();
        }
        public async Task<IActionResult> Index()
        {
            var response = await _httpClient.GetAsync("http://localhost:5272/api/Products");
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync();
            var products = JsonConvert.DeserializeObject<List<Product>>(responseBody); // JsonConvert sınıfını kullanın
            ViewBag.Products = products;

            // Anasayfaya APİ den gelen kategorileri getirir
            var response2 = await _httpClient.GetAsync("http://localhost:5272/api/Categories");
            response2.EnsureSuccessStatusCode();
            var responseBody2 = await response2.Content.ReadAsStringAsync();
            var categories = JsonConvert.DeserializeObject<List<Category>>(responseBody2); // JsonConvert sınıfını kullanın
            ViewBag.Categories = categories;

            return View();
        }

        public async Task<IActionResult> Product()
        {
            var response = await _httpClient.GetAsync("http://localhost:5272/api/Products");
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync();
            var products = JsonConvert.DeserializeObject<List<Product>>(responseBody); // JsonConvert sınıfını kullanın
            ViewBag.Products = products;

            // Anasayfaya APİ den gelen kategorileri getirir
            var response2 = await _httpClient.GetAsync("http://localhost:5272/api/Categories");
            response2.EnsureSuccessStatusCode();
            var responseBody2 = await response2.Content.ReadAsStringAsync();
            var categories = JsonConvert.DeserializeObject<List<Category>>(responseBody2); // JsonConvert sınıfını kullanın
            ViewBag.Categories = categories;

            return View();
        }

        // Create (GET değil, doğrudan Index formundan geliyor)
        [HttpPost]
        public async Task<IActionResult> Create(Product product)
        {
            try
            {
                // Model validation kontrolü
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    ViewBag.Error = "Model validation hatası: " + string.Join(", ", errors);
                    return View("Index", product);
                }

                // CreatedDate'i manuel olarak set et
                product.CreatedDate = DateTime.Now;

                // İlişkili nesne gönderilmeyecek
                product.Category = null;

                // Debug için log ekle
                _logger.LogInformation($"Ürün ekleniyor: {product.Name}, Fiyat: {product.Price}, CategoryId: {product.CategoryId}");

                var jsonData = JsonConvert.SerializeObject(product);
                var content = new StringContent(jsonData, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("http://localhost:5272/api/Products", content);

                if (response.IsSuccessStatusCode)
                {
                    ViewBag.Success = "Ürün başarıyla eklendi!";
                    return RedirectToAction("Product");
                }
                else
                {
                    // API'den gelen hata mesajını oku
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Hatası: {response.StatusCode}, İçerik: {errorContent}");
                    ViewBag.Error = $"API Hatası: {response.StatusCode} - {errorContent}";
                    return View("Index", product);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ürün ekleme sırasında hata oluştu");
                ViewBag.Error = $"Hata: {ex.Message}";
                return View("Index", product);
            }
        }

        // Products siler
        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            var response = await _httpClient.DeleteAsync($"http://localhost:5272/api/Products/{id}");

            if (response.IsSuccessStatusCode)
            {
                return RedirectToAction("Product");
            }

            ViewBag.Error = "Ürün silinemedi.";
            return RedirectToAction("Product");
        }

        [HttpPost]
public async Task<IActionResult> Update(Product product)
{
    try
    {
        product.Category = null;

        var jsonData = JsonConvert.SerializeObject(product);
        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");

        var response = await _httpClient.PutAsync($"http://localhost:5272/api/Products/{product.Id}", content);

        if (response.IsSuccessStatusCode)
        {
            TempData["Success"] = "Ürün başarıyla güncellendi!";
            return RedirectToAction("Product");
        }

        var errorContent = await response.Content.ReadAsStringAsync();
        TempData["Error"] = $"API Hatası: {response.StatusCode} - {errorContent}";
        return RedirectToAction("Product");
    }
    catch (Exception ex)
    {
        TempData["Error"] = $"Hata oluştu: {ex.Message}";
        return RedirectToAction("Product");
    }
}



    }
}