using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ECommerce.Data.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace ECommerce.Web.Controllers
{
    public class AdminController : Controller
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<HomeController> _logger;

        public AdminController(ILogger<HomeController> logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();
        }
        public IActionResult Index()
        {
            return View();
        }

        // Create (GET değil, doğrudan Index formundan geliyor)
        [HttpPost]
        public async Task<IActionResult> Create(Product product)
        {
            // İlişkili nesne gönderilmeyecek
            product.Category = null;

            var jsonData = JsonConvert.SerializeObject(product);
            var content = new StringContent(jsonData, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("http://localhost:5272/api/Products", content);

            if (response.IsSuccessStatusCode)
            {
                return RedirectToAction("Index");
            }

            // Hata durumunda ViewBag ile bilgi gönder
            ViewBag.Error = "Ürün eklenemedi!";
            return RedirectToAction("Index");
        }


    }
}