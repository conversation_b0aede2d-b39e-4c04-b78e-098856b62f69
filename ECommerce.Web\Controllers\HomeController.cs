using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using ECommerce.Web.Models;
using Newtonsoft.Json;
using ECommerce.Data.Models;

namespace ECommerce.Web.Controllers;

public class HomeController : Controller
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();
    }


    public async Task<IActionResult> Index()
    {
        var response = await _httpClient.GetAsync("http://localhost:5272/api/Products");
        response.EnsureSuccessStatusCode();
        var responseBody = await response.Content.ReadAsStringAsync();
        var products = JsonConvert.DeserializeObject<List<Product>>(responseBody); // JsonConvert sınıfını kullanın
        ViewBag.Products = products;

        // Anasayfaya APİ den gelen kategorileri getirir
        var response2 = await _httpClient.GetAsync("http://localhost:5272/api/Categories");
        response2.EnsureSuccessStatusCode();
        var responseBody2 = await response2.Content.ReadAsStringAsync();
        var categories = JsonConvert.DeserializeObject<List<Category>>(responseBody2); // JsonConvert sınıfını kullanın
        ViewBag.Categories = categories;

        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
