@model ECommerce.Data.Models.Product
@{
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
    var CategoryList = ViewBag.Categories as List<ECommerce.Data.Models.Category>;
    var ProductList = ViewBag.Products as List<ECommerce.Data.Models.Product>;
    int urunSayisi = 0;
}

<div class="row row-cols-1 row-cols-md-3 g-4">
    <div class="col">
        <div class="card border-success mb-3">
            @foreach (var item in ProductList)
            {
                urunSayisi++;
            }
            <div class="card-header">Products (@urunSayisi)</div>
            <div class="card-body text-success">

                <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="50"
                    aria-valuemin="0" aria-valuemax="50">
                    <div class="progress-bar text-bg-success" style="width: 80%">25%</div>
                </div>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
</div>

<!-- Ürün Ekleme Formu -->
<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Yeni Ürün Ekle</h5>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="Name" class="form-label">Ürün Adı *</label>
                                <input type="text" class="form-control" name="Name" id="Name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="Price" class="form-label">Fiyat *</label>
                                <input type="number" class="form-control" name="Price" id="Price" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="Stock" class="form-label">Stok *</label>
                                <input type="number" class="form-control" name="Stock" id="Stock" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="CategoryId" class="form-label">Kategori *</label>
                                <select class="form-select" name="CategoryId" id="CategoryId" required>
                                    <option value="">Kategori Seçin</option>
                                    @if (CategoryList != null)
                                    {
                                        @foreach (var category in CategoryList)
                                        {
                                            <option value="@category.Id">@category.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="Description" class="form-label">Açıklama</label>
                        <textarea class="form-control" name="Description" id="Description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="ImageFile" class="form-label">Ürün Resmi</label>
                        <input type="file" class="form-control" name="ImageFile" id="ImageFile" accept="image/*">
                        <div class="form-text">Sadece resim dosyaları (.jpg, .jpeg, .png, .gif, .webp) yüklenebilir. Maksimum dosya boyutu: 5MB</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="IsActive" id="IsActive" value="true" checked>
                        <label class="form-check-label" for="IsActive">
                            Aktif
                        </label>
                        <input type="hidden" name="IsActive" value="false">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ürün Ekle
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mesajlar -->
@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}