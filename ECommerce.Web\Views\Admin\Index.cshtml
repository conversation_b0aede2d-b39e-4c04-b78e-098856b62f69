@model ECommerce.Data.Models.Product
@{
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}


@* <form asp-action="Create" method="post">
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Name</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Describedby</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Price</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Stock</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Image</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product Category</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputEmail1" class="form-label">Product İs Active</label>
    <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
  </div>
  <div class="mb-3">
    <label for="exampleInputPassword1" class="form-label">Password</label>
    <input type="password" class="form-control" id="exampleInputPassword1">
  </div>
  <div class="mb-3 form-check">
    <input type="checkbox" class="form-check-input" id="exampleCheck1">
    <label class="form-check-label" for="exampleCheck1">Check me out</label>
  </div>
  <button type="submit" class="btn btn-primary">Submit</button>
</form> *@

<h2>Yeni Ürün Ekle</h2>

<form asp-action="Create" method="post">
    <input type="text" name="Name" placeholder="Ürün Adı" required />
    <textarea name="Description" placeholder="Açıklama"></textarea>
    <input type="number" name="Price" step="0.01" placeholder="Fiyat" required />
    <input type="number" name="Stock" placeholder="Stok" required />
    <input type="text" name="ImageUrl" placeholder="Resim URL" />
    <input type="number" name="CategoryId" placeholder="Kategori ID" required />
    <input type="checkbox" name="IsActive" checked /> Aktif mi?
    <button type="submit">Ekle</button>
</form>

@if (ViewBag.Error != null)
{
    <div style="color:red">@ViewBag.Error</div>
}