@model ECommerce.Data.Models.Product
@{
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
    var CategoryList = ViewBag.Categories as List<ECommerce.Data.Models.Category>;
    var ProductList = ViewBag.Products as List<ECommerce.Data.Models.Product>;
    int urunSayisi = 0;
}

<div class="row row-cols-1 row-cols-md-3 g-4">
    <div class="col">
        <div class="card border-success mb-3">
            @foreach (var item in ProductList)
            {
                urunSayisi++;
            }
            <div class="card-header">Products (@urunSayisi)</div>
            <div class="card-body text-success">

                <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="50"
                    aria-valuemin="0" aria-valuemax="50">
                    <div class="progress-bar text-bg-success" style="width: 80%">25%</div>
                </div>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card border-secondary mb-3">
            <div class="card-header">Header</div>
            <div class="card-body text-secondary">
                <h5 class="card-title">Secondary card title</h5>
                <p class="card-text">Some quick example text to build on the card title and make up the bulk of the
                    card’s content.</p>
            </div>
        </div>
    </div>
</div>