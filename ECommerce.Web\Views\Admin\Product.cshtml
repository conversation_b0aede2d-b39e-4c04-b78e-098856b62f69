@model ECommerce.Data.Models.Product
@{
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
    var CategoryList = ViewBag.Categories as List<ECommerce.Data.Models.Category>;
    var ProductList = ViewBag.Products as List<ECommerce.Data.Models.Product>;
}

<link rel="stylesheet" href="~/css/Admin/product.css" />

<h2><PERSON><PERSON></h2>

<form asp-action="Create" method="post" class="product-form">
    <div style="margin-bottom: 15px;">
        <label for="Name">Ürün Adı:</label>
        <input type="text" name="Name" id="Name" placeholder="Ürün Adı" required
            style="width: 100%; padding: 8px; margin-top: 5px;" />
    </div>

    <div style="margin-bottom: 15px;">
        <label for="Description">Açıklama:</label>
        <textarea name="Description" id="Description" placeholder="Açıklama"
            style="width: 100%; padding: 8px; margin-top: 5px; height: 80px;"></textarea>
    </div>

    <div style="margin-bottom: 15px;">
        <label for="Price">Fiyat:</label>
        <input type="number" name="Price" id="Price" step="0.01" placeholder="Fiyat" required
            style="width: 100%; padding: 8px; margin-top: 5px;" />
    </div>

    <div style="margin-bottom: 15px;">
        <label for="Stock">Stok:</label>
        <input type="number" name="Stock" id="Stock" placeholder="Stok" required
            style="width: 100%; padding: 8px; margin-top: 5px;" />
    </div>

    <div style="margin-bottom: 15px;">
        <label for="ImageUrl">Resim URL:</label>
        <input type="text" name="ImageUrl" id="ImageUrl" placeholder="Resim URL"
            style="width: 100%; padding: 8px; margin-top: 5px;" />
    </div>

    <div style="margin-bottom: 15px;">
        <label for="CategoryId">Kategori ID:</label>
        <select type="number" name="CategoryId" id="CategoryId" class="form-select" aria-label="Default select example">
            <option selected>Kategori Seçin</option>
            @foreach (var category in CategoryList)
            {
                <option value="@category.Id">@category.Name</option>
            }
        </select>
    </div>

    <div style="margin-bottom: 15px;">
        <label>
            <input type="checkbox" name="IsActive" value="true" checked /> Aktif mi?
        </label>
        <input type="hidden" name="IsActive" value="false" />
    </div>

    <button type="submit"
        style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Ürün
        Ekle</button>
</form>

@if (ViewBag.Error != null)
{
    <div
        style="color:red; margin-top: 10px; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
        @ViewBag.Error
    </div>
}

@if (ViewBag.Success != null)
{
    <div
        style="color:green; margin-top: 10px; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;">
        @ViewBag.Success
    </div>
}


<h1 style="margin-bottom:var(--gap)">Ürün Kartları</h1>
<div class="card-container">

    <!-- Kart 1 -->
    @foreach (var product in ProductList)
    {
        <div class="card">
            <img src="@product.ImageUrl" alt="@product.Name">
            <div class="card-body">
                <h3>@product.Name</h3>
                <p>@product.Description</p>
            </div>
            <div class="card-actions">
                <button class="btn-view">İncele</button>
                <button class="btn-edit" data-bs-toggle="modal" data-bs-target="#exampleModal"
                    onclick="productUpdate('@product.Id','@product.Name', '@product.Description', '@product.Price', '@product.Stock','@product.IsActive','@product.CategoryId')">Düzenle</button>
                <form asp-action="Delete" method="post">
                    <input type="hidden" name="id" value="@product.Id" />
                    <button href="submit" class="btn-delete"
                        onclick="return confirm('Ürünü silmek istediğinize emin misiniz?')">Sil</button>
                </form>
            </div>
        </div>
    }

    <script>
        function productUpdate(id, name, description, price, stock, isActive, categoryId) {
            document.getElementById('id').value = id;
            document.getElementById('nameUpdate').value = name;
            document.getElementById('descriptionUpdate').value = description;
            document.getElementById('priceUpdate').value = price;
            document.getElementById('stockUpdate').value = stock;
            document.getElementById('isActiveUpdate').checked = (isActive === 'True' || isActive === true);
            document.getElementById('categoryUpdate').value = categoryId;
        }
    </script>



    <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Modal title</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form asp-action="Update" method="post">
                        <div class="container">
                            <div class="row">
                                <div class="col">
                                    <input type="hidden" class="form-control" id="id" name="Id">
                                    <div class="mb-3">
                                        <label for="exampleInputEmail1" class="form-label">Ürün Adı</label>
                                        <input type="text" class="form-control" id="nameUpdate" name="Name">
                                    </div>
                                    <div class="mb-3">
                                        <label for="exampleInputEmail1" class="form-label">Ürün Açıklaması</label>
                                        <input type="text" class="form-control" id="descriptionUpdate"
                                            name="Description">
                                    </div>
                                    <div class="mb-3">
                                        <label for="exampleInputEmail1" class="form-label">Ürün Fiyatı</label>
                                        <input type="text" class="form-control" id="priceUpdate" name="Price">
                                    </div>
                                    <div class="mb-3">
                                        <label for="exampleInputEmail1" class="form-label">Ürün Stok</label>
                                        <input type="text" class="form-control" id="stockUpdate" name="Stock">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="mb-3">
                                        <label for="categoryUpdate" class="form-label">Ürün Kategorisi</label>
                                        <select class="form-select" id="categoryUpdate" name="CategoryId">
                                            <option disabled selected>Kategori Seçin</option>
                                            @foreach (var category in CategoryList)
                                            {
                                                <option value="@category.Id">@category.Name</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="isActiveUpdate"
                                            name="IsActive">
                                        <label class="form-check-label" for="isActiveUpdate">Ürün aktif mi?</label>
                                    </div>
                                    @if (TempData["Success"] != null)
                                    {
                                        <div class="alert alert-success mt-3">
                                            @TempData["Success"]
                                        </div>
                                    }
                                    @if (TempData["Error"] != null)
                                    {
                                        <div class="alert alert-danger mt-3">
                                            @TempData["Error"]
                                        </div>
                                    }



                                </div>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                            <button type="submit" class="btn btn-primary">Kaydet</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>