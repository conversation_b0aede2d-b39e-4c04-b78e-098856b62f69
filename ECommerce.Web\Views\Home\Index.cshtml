@{
  ViewData["Title"] = "Home Page";
}

@{
  var ProductList = ViewBag.Products as List<ECommerce.Data.Models.Product>;
  var CategoryList = ViewBag.Categories as List<ECommerce.Data.Models.Category>;
}

<link rel="stylesheet" href="~/css/home.css" />


<section class="hero">
  <!-- Sol kısım: Başlık, açıklama, buton -->
  <div class="hero-content">
    <h1>Bahar İndirimi</h1>
    <p>%50'ye Varan İndirim</p>
    <a href="#" class="btn">ŞİMDİ ALIŞVERİŞ YAPIN</a>
  </div>

  <!-- Sağ kısım: <PERSON>r<PERSON>n görseli -->
  <div class="hero-image">
    <img src="https://cdn.pixabay.com/photo/2016/01/09/07/44/headphone-1129896_960_720.png" alt="Spring Sale Ürünü">
  </div>
</section>

<!-- Categories -->
<section class="categories">
  @foreach (var item in CategoryList)
  {
    <div class="cat-btn">@item.Name</div>
  }
</section>


<!-- Featured Products -->
<section class="products">
  <h2>Öne Çıkan Ürünler</h2>
  <div class="grid">
    @foreach (var item in ProductList )
    {
      <div class="card">
        <img src="~/img/resim_1.jpg" alt="Earbuds">
        <h3>@item.Name</h3>
        <p>@item.Price</p>
        <button class="btn-sm">Sepete Ekle</button>
      </div>
    }
  </div>
</section>



<!-- Trust Badges -->
<section class="trust">
  <div class="item">
    <!-- örnek SVG ikon -->
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M..." />
    </svg>
    Free Shipping
  </div>
  <div class="item">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M..." />
    </svg>
    Secure Payment
  </div>
  <div class="item">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M..." />
    </svg>
    Customer Support
  </div>
</section>

@* <div class="text-center">
    <h1 class="display-4">Welcome</h1>
    <p>Learn about <a href="https://learn.microsoft.com/aspnet/core">building Web apps with ASP.NET Core</a>.</p>
</div> *@
