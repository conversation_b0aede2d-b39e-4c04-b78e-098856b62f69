

@{
    var ProductList = ViewBag.Products as List<ECommerce.Data.Models.Product>;
}

<h2><PERSON><PERSON><PERSON><PERSON></h2>

<table class="table">
    <thead>
        <tr>
            <th>Ad</th>
            <th>Fiyat</th>
            <th>Stok</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var product in ProductList)
        {
            <tr>
                <td>@product.Name</td>
                <td>@product.Price ₺</td>
                <td>@product.Stock</td>
            </tr>
        }
    </tbody>
</table>
