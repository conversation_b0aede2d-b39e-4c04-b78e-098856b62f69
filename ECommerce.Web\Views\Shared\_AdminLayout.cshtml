<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 1) Bootstrap CSS & Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>

<body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="~/img/admin_ikon.png" alt="Logo" style="width: 30px;" class="d-inline-block align-text-top">
                Admin Panel
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Pricing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link disabled" aria-disabled="true">Disabled</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>




    <div" class="container">
        <main role="main" class="pb-3">


            <div style="text-align: center; margin-top: 20px;" class="alert alert-dark" role="alert">
                <h3>
                    <i class="fa-solid fa-user-tie"></i>
                    Admin Panel
                </h3>
            </div>

            <div class="container">
                <div class="row">
                    <div style="text-align: center; margin-top: 20px;" class="col-sm-3">
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action active"
                                aria-current="true">
                                The current button
                            </button>
                            <a href="/Admin/Product" class="list-group-item list-group-item-action">
                                Ürün Ekle 
                            </a>

                            <button type="button" class="list-group-item list-group-item-action">A third button
                                item</button>
                            <button type="button" class="list-group-item list-group-item-action">A fourth button
                                item</button>
                            <button type="button" class="list-group-item list-group-item-action" disabled>A disabled
                                button
                                item</button>
                        </div>
                    </div>
                    <div style="margin-top: 20px; " class="col-sm-9">
                        <div class="row">
                            <div class="col-12 col-sm-12">
                                @RenderBody()
                            </div>
                        </div </div>
                    </div>
                </div>

        </main>
        </div>

        <script src="~/lib/jquery/dist/jquery.min.js"></script>
        <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="~/js/site.js" asp-append-version="true"></script>
        @await RenderSectionAsync("Scripts", required: false)
</body>

</html>