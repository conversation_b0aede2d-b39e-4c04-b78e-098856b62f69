﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ECommerce.Web</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ECommerce.Web.styles.css" asp-append-version="true" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">

    <!-- 1) Bootstrap CSS & Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>

<body>
    <header>
        <!-- 2) Navbar -->
        <nav class="navbar navbar-expand-lg bg-white shadow-sm py-3">
            <div class="container">
                <!-- Logo -->
                <a class="navbar-brand fw-bold text-dark" href="#">LOGO</a>

                <!-- Toggler -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse"
                    data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false"
                    aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Menu -->
                <div class="collapse navbar-collapse" id="mainNavbar">
                    <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                        <li class="nav-item px-2">
                            <a class="nav-link active text-dark" aria-current="page" href="/Home">Home</a>
                        </li>
                        <li class="nav-item px-2">
                            <a class="nav-link text-dark" href="/Home/Privacy">Privacy</a>
                        </li>
                        <li class="nav-item px-2">
                            <a class="nav-link text-dark" href="/Product">Product</a>
                        </li>
                    </ul>

                    <!-- Search -->
                    <form class="d-flex me-4" role="search">
                        <div class="search-container">
                            <i class="bi bi-search"></i>
                            <input type="text" placeholder="Search">
                        </div>
                    </form>

                    <!-- Icons -->
                    <div class="d-flex align-items-center">
                        <a href="#" class="text-dark fs-5 me-3">
                            <i class="bi bi-person"></i>
                        </a>
                        <a href="#" class="text-dark fs-5">
                            <i class="bi bi-cart"></i>
                        </a>
                    </div>
                </div>
            </div>
        </nav>

    </header>
    <div style="margin-bottom: 40px;" class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - ECommerce.Web - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>

    <!-- 3) Bootstrap JS (dropdown, toggler vs.) -->
    @* <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script> *@
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
