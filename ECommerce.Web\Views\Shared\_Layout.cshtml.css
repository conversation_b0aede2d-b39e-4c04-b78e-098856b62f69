/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a {
  color: #0077cc;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top {
  border-top: 1px solid #e5e5e5;
}
.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy {
  font-size: 1rem;
  line-height: inherit;
}

/* .footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
} */

 footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    color: white;
    padding: 20px;
    text-align: center;
}

/* --------------------------------------- */

/* --- Özel Stil (istersen ayrı bir .css dosyasına alabilirsin) --- */
            .navbar {
                /* Görsele uygun hafif gölge */
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            }

            .navbar .nav-link {
                font-weight: 500;
            }

            .navbar .nav-link:hover {
                color: #0d6efd;
                /* Bootstrap primary mavisi */
            }

            .navbar-toggler {
                /* Toggler ikonunun renklenmesi */
                filter: invert(20%);
            }

            .form-control-sm {
                min-width: 200px;
            }

            .btn-primary {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }

            .btn-primary:hover {
                background-color: #0b5ed7;
                border-color: #0a58ca;
            }

            /* Container ayarları */
            .search-container {
                position: relative;
                width: 200px;
                /* Genişliği ihtiyaca göre ayarlayın */
                margin: 0px auto;
                /* Ortalamak için */
            }

            /* Arama ikonu */
            .search-container .bi-search {
                position: absolute;
                top: 50%;
                left: 16px;
                transform: translateY(-50%);
                font-size: 1rem;
                /* İkon boyutu */
                color: #6c757d;
                /* İkon rengi (gri ton) */
            }

            /* Input kutusu */
            .search-container input[type="text"] {
                width: 100%;
                padding: 10px 16px 10px 44px;
                /* Solda ikona yer açmak için sol padding geniş */
                border: 1px solid #dee2e6;
                /* Hafif gri çerçeve */
                border-radius: 50px;
                /* Tamamen yuvarlak kenarlar */
                font-size: 1rem;
                outline: none;
                transition: border-color .2s ease-in-out, box-shadow .2s ease-in-out;
            }

            /* Odaklandığında (focus) */
            .search-container input[type="text"]:focus {
                border-color: #0d6efd;
                /* Bootstrap primary mavisi */
                box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            }