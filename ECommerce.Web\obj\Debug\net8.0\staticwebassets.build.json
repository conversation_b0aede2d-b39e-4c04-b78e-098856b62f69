{"Version": 1, "Hash": "VUYKEk4twa0PDSlhNx0t/Q4xyTXBo7c+hMZGpqtO/KU=", "Source": "ECommerce.Web", "BasePath": "_content/ECommerce.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ECommerce.Web\\wwwroot", "Source": "ECommerce.Web", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ECommerce.Web.styles.css", "SourceId": "ECommerce.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "ECommerce.Web.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ECommerce.Web.styles.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ECommerce.Web.bundle.scp.css", "SourceId": "ECommerce.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "ECommerce.Web.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ECommerce.Web.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\css\\home.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "css/home.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\home.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\css\\site.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\favicon.ico", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\img\\admin_ikon.png", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "img/admin_ikon.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\admin_ikon.png"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\img\\resim_1.jpg", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "img/resim_1.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\resim_1.jpg"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\js\\site.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "ECommerce.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Ticaret\\ECommerceSolution\\ECommerce.Web\\wwwroot\\", "BasePath": "_content/ECommerce.Web", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}]}