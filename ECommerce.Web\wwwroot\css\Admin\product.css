/* ----------------------------
   <PERSON><PERSON> Reset ve Box‑Sizing
----------------------------- */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ----------------------------
   Form <PERSON><PERSON><PERSON>ri
----------------------------- */
.product-form {
  /* max-width: 600px; */
  width: auto;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

/* ----------------------------
   <PERSON>ı
----------------------------- */
.product-form > div {
  margin-bottom: 1.25rem;
}

/* ----------------------------
   Label Stilleri
----------------------------- */
.product-form label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

/* ----------------------------
   Girdi (Input / Textarea / Select)
----------------------------- */
.product-form input[type="text"],
.product-form input[type="number"],
.product-form textarea,
.product-form select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.product-form textarea {
  resize: vertical;
  min-height: 100px;
}

.product-form input:focus,
.product-form textarea:focus,
.product-form select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.15);
  outline: none;
}

/* ----------------------------
   Checkbox Satırı
----------------------------- */
.product-form .checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.product-form .checkbox-group input[type="checkbox"] {
  width: auto;
  transform: scale(1.1);
}

/* ----------------------------
   Buton
----------------------------- */
.product-form button[type="submit"] {
  display: inline-block;
  padding: 0.75rem 2rem;
  background-color: #007bff;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.product-form button[type="submit"]:hover {
  background-color: #0056b3;
}

.product-form button[type="submit"]:active {
  transform: translateY(1px);
}

/* ----------------------------
   Responsive Düzen
----------------------------- */
@media (min-width: 768px) {
  .product-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 2rem;
  }
  /* Son iki alan tam genişlikte alt satıra */
  .product-form > div:nth-last-child(-n+2) {
    grid-column: 1 / -1;
  }
}


/* Product Kart  */

 :root {
      --primary: #0d6efd;
      --danger: #dc3545;
      --warning: #ffc107;
      --gray-light: #f1f3f5;
      --gray-medium: #dee2e6;
      --text: #212529;
      --radius: 8px;
      --gap: 1rem;
    }

    /* * { box-sizing: border-box; margin: 0; padding: 0; } */
    body {
      font-family: Arial, sans-serif;
      background: #fafafa;
      color: var(--text);
      padding: var(--gap);
    }

    /* Kartları saran konteyner */
    .card-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--gap);
    }

    /* Temel kart stili */
    .card {
      background: white;
      border: 1px solid var(--gray-medium);
      border-radius: var(--radius);
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    /* Kart içeriği */
    .card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .card-body {
      flex: 1;
      padding: var(--gap);
    }

    .card-body h3 {
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }

    .card-body p {
      font-size: 0.9rem;
      color: #555;
      margin-bottom: 1rem;
      line-height: 1.3;
    }

    /* Buton grubu */
    .card-actions {
      display: flex;
      gap: 0.5rem;
      padding: var(--gap);
      border-top: 1px solid var(--gray-medium);
      background: var(--gray-light);
    }

    .card-actions button {
      flex: 1;
      padding: 0.5rem;
      font-size: 0.9rem;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      transition: background 0.2s;
    }
    .card-actions .btn-view {
      background: var(--primary);
      color: white;
    }
    .card-actions .btn-view:hover {
      background: #0b5ed7;
    }
    .card-actions .btn-edit {
      background: var(--warning);
      color: #212529;
    }
    .card-actions .btn-edit:hover {
      background: #e0a800;
    }
    .card-actions .btn-delete {
      background: var(--danger);
      color: white;
    }
    .card-actions .btn-delete:hover {
      background: #c82333;
    }
