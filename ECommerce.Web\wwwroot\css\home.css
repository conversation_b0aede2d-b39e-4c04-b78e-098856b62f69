
    /* --- Global Reset ve Box‑Sizing --- */
    *, *::before, *::after {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
    }

    a {
      text-decoration: none;
      color: inherit;
    }

    /* --- Hero Section --- */
    .hero {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2rem;
      background-color: #fff;
    }

    /* Sol bölüm: Metin + <PERSON><PERSON> */
    .hero-content {
      flex: 1;
      max-width: 45%;
    }
    .hero-content h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }
    .hero-content p {
      font-size: 1.25rem;
      margin-bottom: 1.5rem;
      color: #555;
    }
    .hero-content .btn {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      background-color: #0d6efd;
      color: #fff;
      font-size: 1rem;
      border-radius: 0.375rem;
      transition: background-color 0.2s ease;
    }
    .hero-content .btn:hover {
      background-color: #0b5ed7;
    }

    /* Sağ bölüm: <PERSON><PERSON>rsel */
    .hero-image {
      flex: 1;
      max-width: 40%;
      text-align: right;
    }
    .hero-image img {
      max-width: 100%;
      height: auto;
      border-radius: 0.5rem;
    }

    /* --- Responsive Ayarlar --- */
    @media (max-width: 992px) {
      .hero {
        flex-direction: column-reverse;
        text-align: center;
      }
      .hero-content,
      .hero-image {
        max-width: 100%;
      }
      .hero-image {
        margin-bottom: 2rem;
      }
    }

    @media (max-width: 576px) {
      .hero-content h1 {
        font-size: 2rem;
      }
      .hero-content p {
        font-size: 1rem;
      }
      .hero-content .btn {
        width: 100%;
        padding: 0.75rem;
      }
    }
     /* ---------- Değişkenler ---------- */
    :root {
      --primary: #0d6efd;
      --gray-light: #f1f3f5;
      --gray-medium: #dee2e6;
      --text: #212529;
      --text-light: #6c757d;
    }

    /* ---------- Reset & Temel ---------- */
    *, *::before, *::after {
      margin: 0; padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: Arial, sans-serif;
      color: var(--text);
      background: #fff;
      line-height: 1.4;
    }
    a { text-decoration: none; color: inherit; }

    img { display: block; max-width: 100%; height: auto; }

    /* ---------- Featured Products ---------- */
    .products {
      padding: 2rem;
    }
    .products h2 {
      font-size: 1.75rem; margin-bottom: 1rem;
    }
    .grid {
      display: grid;
      gap: 1.5rem;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    .card {
      background: white;
      border: 1px solid var(--gray-medium);
      border-radius: .5rem;
      padding: 1rem;
      text-align: center;
    }
    .card img { margin-bottom: .75rem; }
    .card h3 {
      font-size: 1rem; margin-bottom: .25rem;
    }
    .card p {
      font-size: .9rem; color: var(--text-light);
      margin-bottom: .75rem;
    }
    .card .btn-sm {
      /* padding: .5rem 1rem;
      background: var(--primary);
      color: white;
      border-radius: 1rem;
      font-size: .9rem;
      transition: background .2s; */
      text-align: center;
      padding: 1rem;
      background: #93b2e1;
      border-radius: .5rem;
      font-weight: 500;
      cursor: pointer;
      transition: background .2s;
      border: none;
    }
    .card .btn-sm:hover {
      background: #4c7eca;
    }

    /* ---------- Categories ---------- */
    .categories {
      display: flex;
      gap: 1rem;
      padding: 0 2rem 2rem;
      flex-wrap: wrap;
      justify-content: center;
    }
    .categories .cat-btn {
      flex: 1 1 120px;
      text-align: center;
      padding: 1rem;
      background: var(--gray-light);
      border-radius: .5rem;
      font-weight: 500;
      cursor: pointer;
      transition: background .2s;
    }
    .categories .cat-btn:hover {
      background: var(--gray-medium);
    }

     /* ---------- Trust & Footer ---------- */
    .trust {
      display: flex; gap: 2rem;
      padding: 2rem;
      border-top: 1px solid var(--gray-medium);
      border-bottom: 1px solid var(--gray-medium);
      justify-content: center;
      flex-wrap: wrap;
    }
    .trust .item {
      display: flex; align-items: center; gap: .5rem;
      font-size: .95rem; color: var(--text-light);
    }
    .trust .item svg {
      width:1.25rem; height:1.25rem;
    }

    /* .footer {
      padding: 1rem 2rem;
      font-size: .85rem;
      display: flex; justify-content: space-between;
      flex-wrap: wrap;
      color: var(--text-light);
    } */

    /* ---------- Responsive ---------- */
    @media (max-width: 992px) {
      .hero { flex-direction: column; text-align: center; }
      .hero-image { order: -1; margin-bottom: 2rem; }
      .hero-content, .hero-image { max-width: 100%; }
    }
    @media (max-width: 576px) {
      .navbar { flex-direction: column; gap: .5rem; }
      .navbar .search-cart { width: 100%; justify-content: space-between; }
      .hero-content h1 { font-size: 2rem; }
      .hero-content p { font-size: 1rem; }
    }
